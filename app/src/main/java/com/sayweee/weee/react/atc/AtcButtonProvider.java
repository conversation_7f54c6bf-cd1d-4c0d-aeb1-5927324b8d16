package com.sayweee.weee.react.atc;

import android.app.Activity;
import android.util.Log;
import android.view.View;

import androidx.activity.ComponentActivity;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.facebook.react.uimanager.ThemedReactContext;
import com.margelo.nitro.weee.data.AtcProductData;
import com.margelo.nitro.weee.service.IAtcButtonProvider;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.ProductBean;

import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.widget.op.CartOpLayout;

import java.util.HashMap;
import java.util.Map;

/**
 * Adapter that wraps CartOpLayout to implement IAtcUIComponent interface
 * This bridges the main app's CartOpLayout with the nitro module's abstraction
 */
public class AtcButtonProvider implements IAtcButtonProvider {

    private CartOpLayout cartOpLayout;
    private IAtcButtonProvider.AtcOperationListener operationListener;

    @Override
    public View getRootView() {
        return cartOpLayout;
    }

    @Override
    public void initialize(ThemedReactContext context) {
        // Create CartOpLayout instance
        cartOpLayout = new CartOpLayout(context);
        cartOpLayout.setLayoutParams(new CartOpLayout.LayoutParams(200, 100));

        Activity act = context.getCurrentActivity();
        if (act instanceof LifecycleOwner) {
            SharedViewModel.get().productOpStatusData.observe((LifecycleOwner) context.getCurrentActivity(), new Observer<Integer>() {
                @Override
                public void onChanged(Integer integer) {
                    if (operationListener != null) {
                        operationListener.onUpdate();
                    }
                }
            });
        }

        // Note: setupInternalListeners() is called in initializeWithProductData()
        // after OpHelper.helperOp() to prevent the listener from being overwritten
    }

    @Override
    public void initializeWithProductData(AtcProductData product) {
        if (cartOpLayout != null && product != null) {
            try {
//                ProductBean productBean = JsonUtils.parseObject(productJson, ProductBean.class);
                // Convert AtcProductData to ProductBean
                ProductBean productBean = convertAtcProductDataToProductBean(product);

                if (productBean != null) {
                    // Call OpHelper.helperOp to set up the CartOpLayout properly
                    // This is the key missing piece that makes animations work
                    String source = "nitro_atc_component";
                    Map<String, Object> element = new HashMap<>();
                    Map<String, Object> ctx = new HashMap<>();

                    OpHelper.helperOp(cartOpLayout, productBean, productBean, source, element, ctx);

                    // IMPORTANT: Wrap the OpHelper listener instead of replacing it
                    // This preserves the animation and cart logic while adding our callbacks
                    setupWrappedListeners(productBean, source, element, ctx);

                    SimplePreOrderBean.ItemsBean item = OrderManager.get()
                            .getSimpleOrderItem(productBean.getProductId(), productBean.product_key);
                    cartOpLayout.setTextNum(item.quantity);
                }
            } catch (Exception e) {
                Log.e("CartOpLayoutUIComponent", "Error in initializeWithProductData", e);
            }
        }
    }

    @Override
    public void setOperationListener(AtcOperationListener listener) {
        this.operationListener = listener;
    }

    /**
     * Setup wrapped event listeners that preserve OpHelper logic while adding our callbacks
     * Since we can't access the original listener, we recreate the OpHelper logic here
     */
    private void setupWrappedListeners(ProductBean productBean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        if (cartOpLayout == null) {
            return;
        }

        CartOpLayout.OnCartOpListener listener = getOriginalListener(cartOpLayout);
        CartOpLayout.OnCartOpListener wrappedListener = new CartOpLayout.OnCartOpListener() {

            @Override
            public void operateLeft(View view) {
                if (listener != null) {
                    listener.operateLeft(view);
                }
                if (operationListener != null) {
                    operationListener.onUpdate();
                }
            }

            @Override
            public void operateRight(View view) {
                if (listener != null) {
                    listener.operateRight(view);
                }
                if (operationListener != null) {
                    operationListener.onUpdate();
                }
            }

            @Override
            public void onNumClick(View view) {
                if (listener != null) {
                    listener.onNumClick(view);
                }
                if (operationListener != null) {
                    operationListener.onUpdate();
                }
            }

            @Override
            public int getProductId() {
                if (operationListener != null) {
                    return operationListener.getProductId();
                }
                if (productBean != null) {
                    return productBean.getProductId();
                }
                return 0;
            }
        };

        cartOpLayout.setOnOperateListener(wrappedListener);
    }

    /**
     * Get the original listener from CartOpLayout using reflection
     * This preserves the existing functionality while adding our custom listener
     */
    private CartOpLayout.OnCartOpListener getOriginalListener(CartOpLayout cartOpLayout) {
        try {
            // Use reflection to get the current listener
            java.lang.reflect.Field listenerField = CartOpLayout.class.getDeclaredField("listener");
            listenerField.setAccessible(true);
            Object listener = listenerField.get(cartOpLayout);

            if (listener instanceof CartOpLayout.OnCartOpListener) {
                return (CartOpLayout.OnCartOpListener) listener;
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Convert AtcProductData to ProductBean for OpHelper compatibility
     */
    private ProductBean convertAtcProductDataToProductBean(AtcProductData product) {
        try {
            ProductBean productBean = new ProductBean();
            productBean.id = product.getId();
            productBean.product_key = product.getProductKey();
            productBean.min_order_quantity = product.getMinOrderQuantity();
            productBean.max_order_quantity = product.getMaxOrderQuantity();
            productBean.volume_threshold = product.getVolumeThreshold();
            productBean.name = product.getName();
            productBean.price = product.getPrice();
            productBean.volume_price = product.getVolumePrice();
            productBean.product_type = product.getProductType();
//            productBean.refer_value = product.getReferValue();
            return productBean;
        } catch (Exception e) {
            Log.e("CartOpLayoutUIComponent", "Error converting AtcProductData to ProductBean", e);
            return null;
        }
    }
}
